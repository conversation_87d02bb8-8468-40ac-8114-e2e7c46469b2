{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h2 class="fw-bold">إدارة الحضور والانصراف</h2>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('checkin') }}" class="btn btn-success">
            <i class="fas fa-clock"></i> تسجيل حضور
        </a>
        <button class="btn btn-primary" onclick="exportAttendance()">
            <i class="fas fa-download"></i> تصدير
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">التاريخ</label>
                    <input type="date" class="form-control" name="date" value="{{ date_filter }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="{{ url_for('attendance') }}" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>التاريخ</th>
                        <th>وقت الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in attendances.items %}
                    <tr>
                        <td>{{ attendance.employee.full_name }}</td>
                        <td>{{ attendance.employee.employee_number }}</td>
                        <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if attendance.check_in %}
                                <span class="badge bg-success">{{ attendance.check_in.strftime('%H:%M') }}</span>
                            {% else %}
                                <span class="badge bg-secondary">لم يسجل</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.check_out %}
                                <span class="badge bg-info">{{ attendance.check_out.strftime('%H:%M') }}</span>
                            {% else %}
                                <span class="badge bg-warning">لم ينصرف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.status == 'present' %}
                                <span class="badge bg-success">حاضر</span>
                            {% elif attendance.status == 'absent' %}
                                <span class="badge bg-danger">غائب</span>
                            {% elif attendance.status == 'late' %}
                                <span class="badge bg-warning">متأخر</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not attendance.check_out %}
                                <form method="POST" action="{{ url_for('checkout', id=attendance.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-sign-out-alt"></i> انصراف
                                    </button>
                                </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if attendances.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if attendances.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('attendance', page=attendances.prev_num, date=date_filter) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in attendances.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != attendances.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('attendance', page=page_num, date=date_filter) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% endif %}
                {% endfor %}
                
                {% if attendances.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('attendance', page=attendances.next_num, date=date_filter) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<script>
function exportAttendance() {
    const date = '{{ date_filter }}';
    window.location.href = `/attendance/export?date=${date}`;
}
</script>
{% endblock %}