{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h2 class="fw-bold">إدارة الموظفين</h2>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة موظف جديد
        </a>
        <button class="btn btn-success" onclick="exportEmployees()">
            <i class="fas fa-download"></i> تصدير
        </button>
        <button class="btn btn-info" onclick="importEmployees()">
            <i class="fas fa-upload"></i> استيراد
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-8">
                    <input type="text" class="form-control" name="search" 
                           placeholder="البحث بالاسم أو الرقم الوظيفي أو الرقم الوطني..." 
                           value="{{ search }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم الكامل</th>
                        <th>الرقم الوظيفي</th>
                        <th>المسمى الوظيفي</th>
                        <th>الرقم الوطني</th>
                        <th>الهاتف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>
                            {% if employee.photo %}
                                <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                     class="rounded-circle" width="40" height="40">
                            {% else %}
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>{{ employee.full_name }}</td>
                        <td>{{ employee.employee_number }}</td>
                        <td>{{ employee.job_title }}</td>
                        <td>{{ employee.national_id }}</td>
                        <td>{{ employee.phone }}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewEmployee({{ employee.id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editEmployee({{ employee.id }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee({{ employee.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if employees.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if employees.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees', page=employees.prev_num, search=search) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in employees.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != employees.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees', page=page_num, search=search) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if employees.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees', page=employees.next_num, search=search) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<script>
function viewEmployee(id) {
    // Implementation for viewing employee details
    alert('عرض تفاصيل الموظف رقم: ' + id);
}

function editEmployee(id) {
    // Implementation for editing employee
    alert('تعديل الموظف رقم: ' + id);
}

function deleteEmployee(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        // Implementation for deleting employee
        alert('حذف الموظف رقم: ' + id);
    }
}

function exportEmployees() {
    // Implementation for exporting employees
    alert('تصدير بيانات الموظفين');
}

function importEmployees() {
    // Implementation for importing employees
    alert('استيراد بيانات الموظفين');
}
</script>
{% endblock %}