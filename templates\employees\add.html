{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h2 class="fw-bold">إضافة موظف جديد</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('employees') }}">الموظفين</a></li>
                <li class="breadcrumb-item active">إضافة موظف</li>
            </ol>
        </nav>
    </div>
</div>

<form method="POST" enctype="multipart/form-data">
    <div class="row">
        <div class="col-md-8">
            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> البيانات الشخصية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرقم الوطني *</label>
                            <input type="text" class="form-control" name="national_id" 
                                   pattern="[0-9]{10,}" title="يجب أن يكون أكثر من 10 أرقام" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد *</label>
                            <input type="date" class="form-control" name="birth_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المدينة *</label>
                            <input type="text" class="form-control" name="city" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان *</label>
                            <textarea class="form-control" name="address" rows="3" required></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Information -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-briefcase"></i> البيانات الوظيفية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرقم الوظيفي *</label>
                            <input type="text" class="form-control" name="employee_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المسمى الوظيفي *</label>
                            <input type="text" class="form-control" name="job_title" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المؤهل العلمي *</label>
                            <select class="form-select" name="qualification" required>
                                <option value="">اختر المؤهل</option>
                                <option value="دبلوم متوسط">دبلوم متوسط</option>
                                <option value="دبلوم عالي">دبلوم عالي</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="ليسانس">ليسانس</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراة">دكتوراة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">التخصص *</label>
                            <input type="text" class="form-control" name="specialization" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الدرجة الوظيفية *</label>
                            <input type="text" class="form-control" name="job_grade" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التعيين *</label>
                            <input type="date" class="form-control" name="hire_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ المباشرة *</label>
                            <input type="date" class="form-control" name="start_date" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Photo Upload -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-camera"></i> صورة الموظف</h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="photo-preview" src="#" alt="معاينة الصورة" 
                             style="width: 200px; height: 200px; object-fit: cover; border-radius: 10px; display: none;">
                        <div id="photo-placeholder" class="bg-light border rounded d-flex align-items-center justify-content-center" 
                             style="width: 200px; height: 200px; margin: 0 auto;">
                            <i class="fas fa-user fa-4x text-muted"></i>
                        </div>
                    </div>
                    <input type="file" class="form-control" name="photo" accept="image/*" onchange="previewPhoto(this)">
                    <small class="text-muted">اختياري - JPG, PNG, GIF (حد أقصى 16MB)</small>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save"></i> حفظ البيانات
                    </button>
                    <a href="{{ url_for('employees') }}" class="btn btn-secondary w-100">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('photo-preview').src = e.target.result;
            document.getElementById('photo-preview').style.display = 'block';
            document.getElementById('photo-placeholder').style.display = 'none';
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const nationalId = document.querySelector('input[name="national_id"]').value;
    if (nationalId.length < 10) {
        e.preventDefault();
        alert('الرقم الوطني يجب أن يكون أكثر من 10 أرقام');
        return false;
    }
});
</script>
{% endblock %}