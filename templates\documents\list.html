{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h2 class="fw-bold">إدارة الوثائق</h2>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('upload_document') }}" class="btn btn-primary">
            <i class="fas fa-upload"></i> رفع وثيقة جديدة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">فلترة حسب الموظف</label>
                    <select class="form-select" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if employee.id|string == selected_employee %}selected{% endif %}>
                                {{ employee.full_name }} - {{ employee.employee_number }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter"></i> فلترة
                    </button>
                    <a href="{{ url_for('documents') }}" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>نوع الوثيقة</th>
                        <th>الموظف</th>
                        <th>تاريخ الرفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents.items %}
                    <tr>
                        <td>{{ document.title }}</td>
                        <td>
                            <span class="badge bg-info">{{ document.document_type }}</span>
                        </td>
                        <td>{{ document.employee.full_name }}</td>
                        <td>{{ document.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="{{ url_for('static', filename='uploads/' + document.file_path) }}" 
                               target="_blank" class="btn btn-sm btn-success">
                                <i class="fas fa-download"></i> تحميل
                            </a>
                            <button class="btn btn-sm btn-danger" onclick="deleteDocument({{ document.id }})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function deleteDocument(id) {
    if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟')) {
        // Implementation for deleting document
        alert('حذف الوثيقة رقم: ' + id);
    }
}
</script>
{% endblock %}