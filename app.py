from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import os
import csv
import io
from sqlalchemy import or_

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hr_system.db'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # Personal Info
    full_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True, nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    city = db.Column(db.String(50), nullable=False)
    address = db.Column(db.Text, nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    
    # Job Info
    employee_number = db.Column(db.String(20), unique=True, nullable=False)
    job_title = db.Column(db.String(100), nullable=False)
    qualification = db.Column(db.String(50), nullable=False)
    specialization = db.Column(db.String(100), nullable=False)
    job_grade = db.Column(db.String(20), nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    photo = db.Column(db.String(200))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    attendances = db.relationship('Attendance', backref='employee', lazy=True)
    documents = db.relationship('Document', backref='employee', lazy=True)
    promotions = db.relationship('Promotion', backref='employee', lazy=True)
    allowances = db.relationship('Allowance', backref='employee', lazy=True)

class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    status = db.Column(db.String(20), default='present')
    notes = db.Column(db.Text)

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)
    file_path = db.Column(db.String(200), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)

class Promotion(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    from_grade = db.Column(db.String(20), nullable=False)
    to_grade = db.Column(db.String(20), nullable=False)
    promotion_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    notes = db.Column(db.Text)

class Allowance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    approval_date = db.Column(db.Date)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
@login_required
def dashboard():
    total_employees = Employee.query.count()
    pending_promotions = Promotion.query.filter_by(status='pending').count()
    pending_allowances = Allowance.query.filter_by(status='pending').count()
    
    # Get employees eligible for promotions and allowances
    eligible_promotions = []
    eligible_allowances = []
    
    for emp in Employee.query.all():
        years_worked = (datetime.now().date() - emp.start_date).days / 365.25
        
        # Check for promotion eligibility (every 4 years, except 10th which needs 5 years)
        last_promotion = Promotion.query.filter_by(employee_id=emp.id, status='approved').order_by(Promotion.promotion_date.desc()).first()
        if last_promotion:
            years_since_promotion = (datetime.now().date() - last_promotion.promotion_date).days / 365.25
            if years_since_promotion >= 4:
                eligible_promotions.append(emp)
        elif years_worked >= 4:
            eligible_promotions.append(emp)
        
        # Check for allowance eligibility (yearly)
        current_year = datetime.now().year
        existing_allowance = Allowance.query.filter_by(employee_id=emp.id, year=current_year).first()
        if not existing_allowance and years_worked >= 1:
            eligible_allowances.append(emp)
    
    return render_template('dashboard.html', 
                         total_employees=total_employees,
                         pending_promotions=pending_promotions,
                         pending_allowances=pending_allowances,
                         eligible_promotions=eligible_promotions,
                         eligible_allowances=eligible_allowances)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))
        flash('اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# Employee Management Routes
@app.route('/employees')
@login_required
def employees():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Employee.query
    if search:
        query = query.filter(or_(
            Employee.full_name.contains(search),
            Employee.employee_number.contains(search),
            Employee.national_id.contains(search)
        ))
    
    employees = query.paginate(page=page, per_page=10, error_out=False)
    return render_template('employees/list.html', employees=employees, search=search)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        # Handle file upload
        photo_filename = None
        if 'photo' in request.files:
            file = request.files['photo']
            if file.filename != '':
                photo_filename = secure_filename(file.filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], photo_filename))
        
        employee = Employee(
            full_name=request.form['full_name'],
            national_id=request.form['national_id'],
            birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date(),
            city=request.form['city'],
            address=request.form['address'],
            phone=request.form['phone'],
            employee_number=request.form['employee_number'],
            job_title=request.form['job_title'],
            qualification=request.form['qualification'],
            specialization=request.form['specialization'],
            job_grade=request.form['job_grade'],
            hire_date=datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date(),
            start_date=datetime.strptime(request.form['start_date'], '%Y-%m-%d').date(),
            photo=photo_filename
        )
        
        db.session.add(employee)
        db.session.commit()
        flash('تم إضافة الموظف بنجاح')
        return redirect(url_for('employees'))
    
    return render_template('employees/add.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # Create admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
    
    app.run(debug=True)