{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h2 class="fw-bold">إدارة الترقيات</h2>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('create_promotion') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إنشاء ترقية جديدة
        </a>
        <button class="btn btn-success" onclick="autoCreatePromotions()">
            <i class="fas fa-magic"></i> إنشاء تلقائي للمستحقين
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">فلترة حسب الحالة</label>
                    <select class="form-select" name="status">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلقة</option>
                        <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>موافق عليها</option>
                        <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>مرفوضة</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter"></i> فلترة
                    </button>
                </div>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>من درجة</th>
                        <th>إلى درجة</th>
                        <th>تاريخ الترقية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for promotion in promotions.items %}
                    <tr>
                        <td>{{ promotion.employee.full_name }}</td>
                        <td><span class="badge bg-secondary">{{ promotion.from_grade }}</span></td>
                        <td><span class="badge bg-success">{{ promotion.to_grade }}</span></td>
                        <td>{{ promotion.promotion_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if promotion.status == 'pending' %}
                                <span class="badge bg-warning">معلقة</span>
                            {% elif promotion.status == 'approved' %}
                                <span class="badge bg-success">موافق عليها</span>
                            {% elif promotion.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوضة</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if promotion.status == 'pending' %}
                                <a href="{{ url_for('approve_promotion', id=promotion.id) }}" 
                                   class="btn btn-sm btn-success">
                                    <i class="fas fa-check"></i> موافقة
                                </a>
                                <button class="btn btn-sm btn-danger" onclick="rejectPromotion({{ promotion.id }})">
                                    <i class="fas fa-times"></i> رفض
                                </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function autoCreatePromotions() {
    if (confirm('هل تريد إنشاء ترقيات تلقائية لجميع الموظفين المستحقين؟')) {
        fetch('/api/employees/eligible-promotions')
            .then(response => response.json())
            .then(data => {
                alert(`تم العثور على ${data.length} موظف مستحق للترقية`);
                // Implementation for auto-creating promotions
            });
    }
}

function rejectPromotion(id) {
    if (confirm('هل أنت متأكد من رفض هذه الترقية؟')) {
        // Implementation for rejecting promotion
        alert('رفض الترقية رقم: ' + id);
    }
}
</script>
{% endblock %}