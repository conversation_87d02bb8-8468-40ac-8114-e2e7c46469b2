{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0"><i class="fas fa-clock"></i> تسجيل الحضور</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">اختر الموظف</label>
                        <select class="form-select" name="employee_id" required>
                            <option value="">اختر الموظف...</option>
                            {% for employee in employees %}
                                <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.employee_number }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الوقت الحالي</label>
                        <input type="text" class="form-control" id="current-time" readonly>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check"></i> تسجيل الحضور
                        </button>
                        <a href="{{ url_for('attendance') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA');
    document.getElementById('current-time').value = timeString;
}

// Update time every second
setInterval(updateTime, 1000);
updateTime(); // Initial call
</script>
{% endblock %}